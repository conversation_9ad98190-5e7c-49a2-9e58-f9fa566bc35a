﻿@inherits LayoutComponentBase
@using CoreHub.Shared.Components
@using System.Security.Claims
@inject NavigationManager NavigationManager
@inject IUserAuthenticationService UserAuthService
@inject AuthenticationStateProvider AuthStateProvider

<MudThemeProvider Theme="@_theme" IsDarkMode="_isDarkMode" />
<MudPopoverProvider />
<MudDialogProvider />
<MudSnackbarProvider />

<MudLayout>
    <MudAppBar Color="Color.Primary" Type="AppBarType.Dense" Elevation="1">
        <MudIconButton Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start"
            OnClick="@((e) => DrawerToggle())" />
        <MudText Typo="Typo.h5" Class="ml-3">企业管理系统</MudText>
        <MudSpacer />

        <!-- 系统设置菜单 -->
        <MudMenu Icon="@Icons.Material.Filled.MoreVert" Color="Color.Inherit" AnchorOrigin="Origin.BottomRight"
            TransformOrigin="Origin.TopRight">
            <ActivatorContent>
                <MudIconButton Icon="@Icons.Material.Filled.MoreVert" Color="Color.Inherit" Title="系统设置" />
            </ActivatorContent>
            <ChildContent>
                <AuthorizeView>
                    <Authorized>
                        <MudMenuItem>
                            <div style="display: flex; align-items: center; pointer-events: none;">
                                <MudIcon Icon="@Icons.Material.Filled.Person" Class="mr-2" />
                                @context.User.FindFirst("DisplayName")?.Value
                            </div>
                        </MudMenuItem>
                        <MudDivider />
                        <MudMenuItem OnClick="HandleLogout">
                            <div style="display: flex; align-items: center;">
                                <MudIcon Icon="@Icons.Material.Filled.Logout" Class="mr-2" />
                                注销
                            </div>
                        </MudMenuItem>
                        <MudDivider />
                    </Authorized>
                    <NotAuthorized>
                        <MudMenuItem OnClick="@(() => NavigationManager.NavigateTo("/login"))">
                            <div style="display: flex; align-items: center;">
                                <MudIcon Icon="@Icons.Material.Filled.Login" Class="mr-2" />
                                登录
                            </div>
                        </MudMenuItem>
                        <MudDivider />
                    </NotAuthorized>
                </AuthorizeView>
                <MudMenuItem OnClick="@DarkModeToggle">
                    <div style="display: flex; align-items: center;">
                        <MudIcon Icon="@(DarkLightModeButtonIcon)" Class="mr-2" />
                        @(_isDarkMode ? "浅色主题" : "深色主题")
                    </div>
                </MudMenuItem>
            </ChildContent>
        </MudMenu>
    </MudAppBar>

    <MudDrawer id="nav-drawer" @bind-Open="_drawerOpen" ClipMode="DrawerClipMode.Always" Elevation="2">
        <NavMenu />
    </MudDrawer>

    @* <MudMainContent Class="pt-16 pa-4"> *@
    <MudMainContent>
        <MudContainer MaxWidth="MaxWidth.ExtraLarge">
            @Body
        </MudContainer>
    </MudMainContent>
</MudLayout>

@code {
    private bool _drawerOpen = true;
    private bool _isDarkMode = false;
    private MudTheme? _theme = null;

    protected override void OnInitialized()
    {
        base.OnInitialized();

        _theme = new()
        {
            PaletteLight = _lightPalette,
            PaletteDark = _darkPalette,
            LayoutProperties = new LayoutProperties()
        };
    }

    private void DrawerToggle()
    {
        _drawerOpen = !_drawerOpen;
    }

    private void DarkModeToggle()
    {
        _isDarkMode = !_isDarkMode;
    }



    private readonly PaletteLight _lightPalette = new()
    {
        Black = "#110e2d",
        AppbarText = "#424242",
        AppbarBackground = "rgba(255,255,255,0.8)",
        DrawerBackground = "#ffffff",
        GrayLight = "#e8e8e8",
        GrayLighter = "#f9f9f9",
    };

    private readonly PaletteDark _darkPalette = new()
    {
        Primary = "#7e6fff",
        Surface = "#1e1e2d",
        Background = "#1a1a27",
        BackgroundGray = "#151521",
        AppbarText = "#92929f",
        AppbarBackground = "rgba(26,26,39,0.8)",
        DrawerBackground = "#1a1a27",
        ActionDefault = "#74718e",
        ActionDisabled = "#9999994d",
        ActionDisabledBackground = "#605f6d4d",
        TextPrimary = "#b2b0bf",
        TextSecondary = "#92929f",
        TextDisabled = "#ffffff33",
        DrawerIcon = "#92929f",
        DrawerText = "#92929f",
        GrayLight = "#2a2833",
        GrayLighter = "#1e1e2d",
        Info = "#4a86ff",
        Success = "#3dcb6c",
        Warning = "#ffb545",
        Error = "#ff3f5f",
        LinesDefault = "#33323e",
        TableLines = "#33323e",
        Divider = "#292838",
        OverlayLight = "#1e1e2d80",
    };

    public string DarkLightModeButtonIcon => _isDarkMode switch
    {
        true => Icons.Material.Rounded.AutoMode,
        false => Icons.Material.Outlined.DarkMode,
    };

    private async Task HandleLogout()
    {
        try
        {
            // 使用 .NET 原生的 AuthenticationStateProvider 进行注销
            if (AuthStateProvider is PersistentAuthenticationStateProvider persistentProvider)
            {
                await persistentProvider.MarkUserAsLoggedOutAsync();
            }

            NavigationManager.NavigateTo("/login", true);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"注销异常: {ex}");
        }
    }
}

<div id="blazor-error-ui">
    An unhandled error has occurred.
    <a href="" class="reload">Reload</a>
    <a class="dismiss">🗙</a>
</div>
